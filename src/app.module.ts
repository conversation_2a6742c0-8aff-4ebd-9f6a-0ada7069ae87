import { Module } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { UserModule } from './user/user.module';
import Config from './configuration';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigEnum } from './enum/config.enum';
import { SessionHistoryModule } from './session_history/session_history.module';
import { AuthModule } from './auth/auth.module';
import { APP_GUARD, APP_FILTER, APP_INTERCEPTOR } from '@nestjs/core';
import { JwtAuthGuard } from './auth/guards/jwt-auth.guard';
import { HttpExceptionFilter } from './common/filters/http-exception.filter';
import { ResponseInterceptor } from './common/interceptors/response.interceptor';
import { DifyModule } from './dify/dify.module';

@Module({
  imports: [
      ConfigModule.forRoot({ isGlobal: true, load: [Config] }),
      UserModule,
      TypeOrmModule.forRootAsync({
        imports: [ConfigModule],
        inject: [ConfigService],
        useFactory: (configService: ConfigService) => {
          return {
            type: 'mysql',
            host: configService.get(ConfigEnum.DB_MYSQL_HOST),
            port: configService.get(ConfigEnum.DB_MYSQL_PORT),
            username: configService.get(ConfigEnum.DB_MYSQL_USER),
            password: configService.get(ConfigEnum.DB_MYSQL_PASSWORD),
            database: configService.get(ConfigEnum.DB_MYSQL_DATABASE),
            entities: [__dirname + '/**/*.entity{.ts,.js}'],
            synchronize: true,
          }
        },
      }),
      SessionHistoryModule,
      AuthModule,
      DifyModule,
    ],
  controllers: [AppController],
  providers: [
    AppService,
    {
      provide: APP_GUARD,
      useClass: JwtAuthGuard,
    },
    {
      provide: APP_FILTER,
      useClass: HttpExceptionFilter,
    },
    {
      provide: APP_INTERCEPTOR,
      useClass: ResponseInterceptor,
    },
  ],
})
export class AppModule {}
