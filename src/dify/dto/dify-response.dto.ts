export class ChatMessageDto {
  query: string;
  response_mode: 'streaming' | 'blocking';
  user: string;
  conversation_id?: string;
  inputs?: Record<string, any>;
  files?: Array<{
    type: 'image' | 'document' | 'audio' | 'video' | 'custom';
    transfer_method: 'remote_url' | 'local_file';
    url?: string;
    upload_file_id?: string;
  }>;
  auto_generate_name?: boolean;
}

export interface DifyStreamEvent {
  event: string;
  task_id?: string;
  id?: string;
  message_id?: string;
  conversation_id?: string;
  answer?: string;
  metadata?: {
    usage?: any;
    retriever_resources?: any[];
  };
  created_at?: number;
  status?: string;
  error?: string;
  code?: string;
  message?: string;
}

export class DifyResponse {
  event: string;
  task_id: string;
  id: string;
  message_id: string;
  conversation_id: string;
  mode: string;
  answer: string;
  metadata: {
    usage: any;
    retriever_resources: any[];
  };
  created_at: number;
}

export class DifyStreamResponse {
  message_id: string;
  conversation_id: string;
  answer: string;
  metadata: {
    usage: any;
    retriever_resources: any[];
  };
  created_at: number;
  task_id: string;
}
