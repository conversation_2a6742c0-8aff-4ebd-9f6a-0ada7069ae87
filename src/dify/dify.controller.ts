import { <PERSON>, Get, Post, Body, Patch, Param, Delete } from '@nestjs/common';
import { DifyService } from './dify.service';
import { CreateDifyDto } from './dto/create-dify.dto';
import { UpdateDifyDto } from './dto/update-dify.dto';

@Controller('dify')
export class DifyController {
  constructor(private readonly difyService: DifyService) {}

  @Post()
  create(@Body() createDifyDto: CreateDifyDto) {
    return this.difyService.create(createDifyDto);
  }

  @Get()
  findAll() {
    return this.difyService.findAll();
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.difyService.findOne(+id);
  }

  @Patch(':id')
  update(@Param('id') id: string, @Body() updateDifyDto: UpdateDifyDto) {
    return this.difyService.update(+id, updateDifyDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.difyService.remove(+id);
  }
}
