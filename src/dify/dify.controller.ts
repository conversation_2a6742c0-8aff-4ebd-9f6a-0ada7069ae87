import {
  Controller,
  Get,
  Post,
  Body,
  Query,
  UseInterceptors,
  UploadedFile,
  HttpException,
  HttpStatus
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { Public } from '../auth/decorators/public.decorator';
import { DifyService } from './dify.service';
import { ChatMessageDto } from './dto/dify-response.dto';

@Controller('dify')
export class DifyController {
  constructor(private readonly difyService: DifyService) {}

  /**
   * 发送聊天消息
   */
  @Post('chat')
  async sendChatMessage(@Body() chatMessageDto: ChatMessageDto) {
    try {
      console.log('Controller收到请求:', chatMessageDto);
      return await this.difyService.sendChatMessage(chatMessageDto);
    } catch (error) {
      console.error('Controller错误:', error);
      throw new HttpException(
        error.message || '发送消息失败',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 发送流式聊天消息
   */
  @Public()
  @Post('chat/streaming')
  async sendChatMessageStreaming(@Body() chatMessageDto: ChatMessageDto) {
    try {
      chatMessageDto.response_mode = 'streaming';
      return await this.difyService.sendChatMessageStreaming(chatMessageDto);
    } catch (error) {
      throw new HttpException(
        error.message || '发送流式消息失败',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 发送阻塞式聊天消息
   */
  @Post('chat/blocking')
  async sendChatMessageBlocking(@Body() chatMessageDto: ChatMessageDto) {
    try {
      chatMessageDto.response_mode = 'blocking';
      return await this.difyService.sendChatMessageBlocking(chatMessageDto);
    } catch (error) {
      throw new HttpException(
        error.message || '发送阻塞消息失败',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 上传文件
   */
  @Post('upload')
  @UseInterceptors(FileInterceptor('file'))
  async uploadFile(
    @UploadedFile() file: any,
    @Body('user') user: string
  ) {
    if (!file) {
      throw new HttpException('请选择要上传的文件', HttpStatus.BAD_REQUEST);
    }

    if (!user) {
      throw new HttpException('用户标识不能为空', HttpStatus.BAD_REQUEST);
    }

    try {
      return await this.difyService.uploadFile(file, user);
    } catch (error) {
      throw new HttpException(
        error.message || '文件上传失败',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 获取会话列表
   */
  @Get('conversations')
  async getConversations(
    @Query('user') user: string,
    @Query('limit') limit?: number,
    @Query('page') page?: number
  ) {
    if (!user) {
      throw new HttpException('用户标识不能为空', HttpStatus.BAD_REQUEST);
    }

    try {
      return await this.difyService.getConversations(
        user,
        limit ? Number(limit) : 20,
        page ? Number(page) : 1
      );
    } catch (error) {
      throw new HttpException(
        error.message || '获取会话列表失败',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }
}
