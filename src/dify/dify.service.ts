import { Injectable } from '@nestjs/common';
import { CreateDifyDto } from './dto/create-dify.dto';
import { UpdateDifyDto } from './dto/update-dify.dto';

@Injectable()
export class DifyService {
  create(createDifyDto: CreateDifyDto) {
    return 'This action adds a new dify';
  }

  findAll() {
    return `This action returns all dify`;
  }

  findOne(id: number) {
    return `This action returns a #${id} dify`;
  }

  update(id: number, updateDifyDto: UpdateDifyDto) {
    return `This action updates a #${id} dify`;
  }

  remove(id: number) {
    return `This action removes a #${id} dify`;
  }
}
