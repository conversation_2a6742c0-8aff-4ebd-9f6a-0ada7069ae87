import { Injectable, HttpException, HttpStatus, Logger } from '@nestjs/common';
import { CreateDifyDto } from './dto/create-dify.dto';
import { UpdateDifyDto } from './dto/update-dify.dto';
import { ConfigService } from '@nestjs/config';
import { HttpService } from '@nestjs/axios';
import { ChatMessageDto, DifyResponse, DifyStreamResponse, DifyStreamEvent } from './dto/dify-response.dto';
import { ConfigEnum } from 'src/enum/config.enum';
import { AxiosResponse } from 'axios';
import { Readable } from 'stream';

@Injectable()
export class DifyService {
  private readonly logger = new Logger(DifyService.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly httpService: HttpService
  ) {}

  /**
   * 发送聊天消息 - Streaming模式
   */
  async sendChatMessageStreaming(chatMessageDto: ChatMessageDto): Promise<DifyStreamResponse> {
    try {
      console.log('chatMessageDto', chatMessageDto);
      const apiKey = this.configService.get<string>(ConfigEnum.DIFY_API_KEY);
      const baseUrl = this.configService.get<string>(ConfigEnum.DIFY_API_BASE_URL);

      console.log('Dify配置:', { apiKey: apiKey ? '***' : 'undefined', baseUrl });

      if (!apiKey || !baseUrl) {
        this.logger.error('Dify配置缺失', { apiKey: !!apiKey, baseUrl: !!baseUrl });
        throw new HttpException('Dify配置缺失', HttpStatus.INTERNAL_SERVER_ERROR);
      }

      const headers = {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
      };

      const data = {
        query: chatMessageDto.query,
        response_mode: 'streaming',
        user: chatMessageDto.user,
        conversation_id: chatMessageDto.conversation_id,
        inputs: chatMessageDto.inputs || {},
        files: chatMessageDto.files || [],
        auto_generate_name: chatMessageDto.auto_generate_name ?? true,
      };

      this.logger.log(`发送Dify消息: ${chatMessageDto.query.substring(0, 50)}...`);

      console.log('请求URL:', `${baseUrl}/chat-messages`);
      console.log('请求头:', headers);
      console.log('请求数据:', JSON.stringify(data, null, 2));

      const response = await this.httpService.axiosRef.post(
        `${baseUrl}/chat-messages`,
        data,
        {
          headers,
          responseType: 'stream',
          timeout: 60000, // 60秒超时
        }
      );

      return await this.parseStreamResponse(response.data);
    } catch (error) {
      // console.log('完整错误信息:', error);
      console.log('错误响应数据:', error.response?.data);

      this.logger.error('Dify API调用失败:', {
        message: error.message,
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        config: {
          url: error.config?.url,
          method: error.config?.method,
          headers: error.config?.headers ? { ...error.config.headers, Authorization: '[HIDDEN]' } : undefined,
          data: error.config?.data
        }
      });

      // 解析Dify API返回的错误信息
      let errorMessage = error.message;
      let errorCode = 'unknown';

      if (error.response?.data) {
        try {
          let errorData;

          // 如果响应数据是Stream，需要读取Buffer
          if (error.response.data._readableState && error.response.data._readableState.buffer) {
            const buffers = error.response.data._readableState.buffer;
            if (buffers.length > 0) {
              const buffer = buffers[0];
              const jsonString = buffer.toString('utf8');
              console.log('从Buffer解析的JSON:', jsonString);
              errorData = JSON.parse(jsonString);
            }
          } else if (typeof error.response.data === 'string') {
            errorData = JSON.parse(error.response.data);
          } else if (typeof error.response.data === 'object') {
            errorData = error.response.data;
          }

          if (errorData) {
            errorCode = errorData.code || 'unknown';
            errorMessage = errorData.message || errorData.error || error.message;

            // 针对特定错误提供更友好的提示
            if (errorCode === 'invalid_param' && errorMessage.includes('Workflow not published')) {
              errorMessage = '工作流未发布。请在Dify平台上发布您的工作流后再试。';
            } else if (errorCode === 'app_unavailable') {
              errorMessage = '应用不可用。请检查应用配置。';
            } else if (errorCode === 'provider_not_initialize') {
              errorMessage = '模型提供商未初始化。请在Dify平台配置模型。';
            }
          }

        } catch (parseError) {
          this.logger.warn('解析Dify错误响应失败:', parseError);
        }
      }

      throw new HttpException(
        `Dify API调用失败 (${errorCode}): ${errorMessage}`,
        error.response?.status || HttpStatus.BAD_REQUEST
      );
    }
  }

  /**
   * 解析流式响应
   */
  private async parseStreamResponse(stream: Readable): Promise<DifyStreamResponse> {
    return new Promise((resolve, reject) => {
      let buffer = '';
      let completeAnswer = '';
      let messageId = '';
      let conversationId = '';
      let taskId = '';
      let metadata: any = null;
      let createdAt = 0;

      stream.on('data', (chunk: Buffer) => {
        buffer += chunk.toString();

        // 按双换行符分割事件
        const events = buffer.split('\n\n');
        buffer = events.pop() || ''; // 保留最后一个可能不完整的事件

        for (const eventStr of events) {
          if (eventStr.trim()) {
            this.processStreamEvent(eventStr, {
              completeAnswer: (answer: string) => { completeAnswer += answer; },
              setMessageId: (id: string) => { messageId = id; },
              setConversationId: (id: string) => { conversationId = id; },
              setTaskId: (id: string) => { taskId = id; },
              setMetadata: (meta: any) => { metadata = meta; },
              setCreatedAt: (time: number) => { createdAt = time; },
            });
          }
        }
      });

      stream.on('end', () => {
        this.logger.log('Dify流式响应完成');
        resolve({
          message_id: messageId,
          conversation_id: conversationId,
          answer: completeAnswer,
          metadata: metadata || { usage: {}, retriever_resources: [] },
          created_at: createdAt,
          task_id: taskId,
        });
      });

      stream.on('error', (error) => {
        this.logger.error('流式响应错误:', error);
        reject(new HttpException('流式响应处理失败', HttpStatus.INTERNAL_SERVER_ERROR));
      });
    });
  }

  /**
   * 处理单个流事件
   */
  private processStreamEvent(eventStr: string, handlers: {
    completeAnswer: (answer: string) => void;
    setMessageId: (id: string) => void;
    setConversationId: (id: string) => void;
    setTaskId: (id: string) => void;
    setMetadata: (meta: any) => void;
    setCreatedAt: (time: number) => void;
  }) {
    try {
      // 提取data:后的JSON内容
      const dataMatch = eventStr.match(/^data:\s*(.+)$/m);
      if (!dataMatch) return;

      const eventData: DifyStreamEvent = JSON.parse(dataMatch[1]);

      switch (eventData.event) {
        case 'message':
          // 累积回答内容
          if (eventData.answer) {
            handlers.completeAnswer(eventData.answer);
          }
          if (eventData.message_id) handlers.setMessageId(eventData.message_id);
          if (eventData.conversation_id) handlers.setConversationId(eventData.conversation_id);
          if (eventData.task_id) handlers.setTaskId(eventData.task_id);
          if (eventData.created_at) handlers.setCreatedAt(eventData.created_at);
          break;

        case 'message_end':
          // 消息结束，保存元数据
          if (eventData.metadata) handlers.setMetadata(eventData.metadata);
          if (eventData.message_id) handlers.setMessageId(eventData.message_id);
          if (eventData.conversation_id) handlers.setConversationId(eventData.conversation_id);
          if (eventData.task_id) handlers.setTaskId(eventData.task_id);
          break;

        case 'error':
          // 错误事件
          this.logger.error(`Dify流式响应错误: ${eventData.message}`);
          throw new HttpException(
            eventData.message || '未知错误',
            HttpStatus.BAD_REQUEST
          );

        case 'workflow_started':
        case 'workflow_finished':
        case 'node_started':
        case 'node_finished':
          // 工作流事件，记录日志
          this.logger.debug(`工作流事件: ${eventData.event}`);
          break;

        case 'ping':
          // 心跳事件，忽略
          break;

        default:
          this.logger.debug(`未处理的事件类型: ${eventData.event}`);
      }
    } catch (error) {
      this.logger.warn(`解析流事件失败: ${error.message}, 事件内容: ${eventStr}`);
    }
  }

  /**
   * 发送聊天消息 - Blocking模式
   */
  async sendChatMessageBlocking(chatMessageDto: ChatMessageDto): Promise<DifyResponse> {
    const apiKey = this.configService.get<string>(ConfigEnum.DIFY_API_KEY);
    const baseUrl = this.configService.get<string>(ConfigEnum.DIFY_API_BASE_URL);

    if (!apiKey || !baseUrl) {
      throw new HttpException('Dify配置缺失', HttpStatus.INTERNAL_SERVER_ERROR);
    }

    const headers = {
      'Authorization': `Bearer ${apiKey}`,
      'Content-Type': 'application/json',
    };

    const data = {
      query: chatMessageDto.query,
      response_mode: 'blocking',
      user: chatMessageDto.user,
      conversation_id: chatMessageDto.conversation_id,
      inputs: chatMessageDto.inputs || {},
      files: chatMessageDto.files || [],
      auto_generate_name: chatMessageDto.auto_generate_name ?? true,
    };

    try {
      this.logger.log(`发送Dify消息(阻塞模式): ${chatMessageDto.query.substring(0, 50)}...`);

      const response = await this.httpService.axiosRef.post<DifyResponse>(
        `${baseUrl}/chat-messages`,
        data,
        { headers, timeout: 60000 }
      );

      return response.data;
    } catch (error) {
      this.logger.error('Dify API调用失败:', error.message);
      throw new HttpException(
        `Dify API调用失败: ${error.message}`,
        HttpStatus.BAD_REQUEST
      );
    }
  }

  /**
   * 根据response_mode选择合适的方法
   */
  async sendChatMessage(chatMessageDto: ChatMessageDto): Promise<DifyStreamResponse | DifyResponse> {
    if (chatMessageDto.response_mode === 'streaming') {
      return this.sendChatMessageStreaming(chatMessageDto);
    } else {
      return this.sendChatMessageBlocking(chatMessageDto);
    }
  }

  /**
   * 上传文件到Dify
   */
  async uploadFile(file: any, user: string): Promise<{ id: string; name: string }> {
    const apiKey = this.configService.get<string>(ConfigEnum.DIFY_API_KEY);
    const baseUrl = this.configService.get<string>(ConfigEnum.DIFY_API_BASE_URL);

    if (!apiKey || !baseUrl) {
      throw new HttpException('Dify配置缺失', HttpStatus.INTERNAL_SERVER_ERROR);
    }

    const formData = new FormData();
    formData.append('file', new Blob([file.buffer]), file.originalname);
    formData.append('user', user);

    try {
      const response = await this.httpService.axiosRef.post(
        `${baseUrl}/files/upload`,
        formData,
        {
          headers: {
            'Authorization': `Bearer ${apiKey}`,
          },
          timeout: 30000,
        }
      );

      return response.data;
    } catch (error) {
      this.logger.error('文件上传失败:', error.message);
      throw new HttpException(
        `文件上传失败: ${error.message}`,
        HttpStatus.BAD_REQUEST
      );
    }
  }

  /**
   * 获取会话列表
   */
  async getConversations(user: string, limit = 20, page = 1): Promise<any> {
    const apiKey = this.configService.get<string>(ConfigEnum.DIFY_API_KEY);
    const baseUrl = this.configService.get<string>(ConfigEnum.DIFY_API_BASE_URL);

    if (!apiKey || !baseUrl) {
      throw new HttpException('Dify配置缺失', HttpStatus.INTERNAL_SERVER_ERROR);
    }

    try {
      const response = await this.httpService.axiosRef.get(
        `${baseUrl}/conversations`,
        {
          headers: {
            'Authorization': `Bearer ${apiKey}`,
          },
          params: { user, limit, page },
          timeout: 10000,
        }
      );

      return response.data;
    } catch (error) {
      this.logger.error('获取会话列表失败:', error.message);
      throw new HttpException(
        `获取会话列表失败: ${error.message}`,
        HttpStatus.BAD_REQUEST
      );
    }
  }
}
