import { Injectable } from '@nestjs/common';
import { CreateDifyDto } from './dto/create-dify.dto';
import { UpdateDifyDto } from './dto/update-dify.dto';
import { ConfigService } from '@nestjs/config';
import {HttpService} from '@nestjs/axios';
import { ChatMessageDto, DifyResponse } from './dto/dify-response.dto';
import { ConfigEnum } from 'src/enum/config.enum';

@Injectable()
export class DifyService {
  constructor(private readonly configService: ConfigService, private readonly httpService: HttpService) {}

  sendChatMessage(chatMessageDto: ChatMessageDto): Promise<DifyResponse> {
    const apiKey = this.configService.get<string>(ConfigEnum.DIFY_API_KEY);
    const baseUrl = this.configService.get<string>(ConfigEnum.DIFY_API_BASE_URL);
    const headers = {
      Authorization: `Bearer ${apiKey}`,
      'Content-Type': 'application/json',
    };
    const data = {
      query: chatMessageDto.query,
      response_mode: chatMessageDto.response_mode,
      user: chatMessageDto.user,
      auto_generate_name: true,
    };
    return this.httpService.post<DifyResponse>(`${baseUrl}/chat/message`, data, { headers }).toPromise();
  }

  uploadFile() {
    return `This action returns all dify`;
  }

  getConversations(id: number) {
    return `This action returns a #${id} dify`;
  }

  handleStreamResponse(id: number, updateDifyDto: UpdateDifyDto) {
    return `This action updates a #${id} dify`;
  }

  remove(id: number) {}
}
