export enum ConfigEnum {
  HTTP_HOST = 'http.host',
  HTTP_PORT = 'http.port',
  DB_MYSQL_HOST = 'db.mysql.host',
  DB_MYSQL_PORT = 'db.mysql.port',
  DB_MYSQL_USER = 'db.mysql.user',
  DB_MYSQL_PASSWORD = 'db.mysql.password',
  DB_MYSQL_DATABASE = 'db.mysql.database',
  JWT_SECRET = 'jwt.secret',
  JWT_ACCESS_TOKEN_EXPIRES_IN = 'jwt.accessTokenExpiresIn',
  JWT_REFRESH_TOKEN_EXPIRES_IN = 'jwt.refreshTokenExpiresIn',
  JWT_REFRESH_TOKEN_SECRET = 'jwt.refreshTokenSecret',
}
