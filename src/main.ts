import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { MyLogger } from './MyLogger';
import * as mysql from 'mysql2'
import { ValidationPipe } from '@nestjs/common';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  // 配置CORS
  app.enableCors({
    origin: [
      'http://localhost:3001',  // 前端开发服务器
      'http://127.0.0.1:3001',  // 本地访问
      'http://localhost:3000',  // 同端口访问（如果需要）
    ],
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS', 'HEAD'],
    allowedHeaders: [
      'Content-Type',
      'Authorization',
      'X-Requested-With',
      'Accept',
      'Origin',
      'Referer',
      'User-Agent',
      'sec-ch-ua',
      'sec-ch-ua-mobile',
      'sec-ch-ua-platform',
      'Platform',
      'Cache-Control',
      'Pragma'
    ],
    credentials: true, // 允许携带cookies和认证信息
    preflightContinue: false,
    optionsSuccessStatus: 204
  });

  app.useGlobalPipes(new ValidationPipe());
  app.setGlobalPrefix('/api');
  app.useLogger(new MyLogger());

  const port = process.env.PORT ?? 3000;
  await app.listen(port);
  console.log(`🚀 应用已启动！`);
  console.log(`📍 服务地址: http://localhost:${port}`);
  console.log(`📍 本地访问: http://127.0.0.1:${port}`);
}
bootstrap();
