import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, LessThan } from 'typeorm';
import { JwtService } from '@nestjs/jwt';
import { RefreshToken } from '../entities/refresh-token.entity';
import { jwtConstants } from '../constants';
import { BusinessException } from '../../common/exceptions/business.exception';

@Injectable()
export class RefreshTokenService {
  constructor(
    @InjectRepository(RefreshToken)
    private refreshTokenRepository: Repository<RefreshToken>,
    private jwtService: JwtService,
  ) {}

  /**
   * 生成Refresh Token
   * @param userId 用户ID
   * @param deviceInfo 设备信息
   * @param ipAddress IP地址
   * @returns Refresh Token字符串
   */
  async generateRefreshToken(
    userId: number,
    deviceInfo?: string,
    ipAddress?: string,
  ): Promise<string> {
    // 生成JWT格式的refresh token
    const payload = {
      sub: userId,
      type: 'refresh',
      iat: Math.floor(Date.now() / 1000),
    };

    const refreshToken = this.jwtService.sign(payload, {
      secret: jwtConstants.refreshTokenSecret,
      expiresIn: jwtConstants.refreshTokenExpiresIn,
    });

    // 计算过期时间
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 7); // 7天后过期

    // 保存到数据库
    const refreshTokenEntity = this.refreshTokenRepository.create({
      token: refreshToken,
      userId,
      expiresAt,
      deviceInfo,
      ipAddress,
    });

    await this.refreshTokenRepository.save(refreshTokenEntity);
    return refreshToken;
  }

  /**
   * 验证Refresh Token
   * @param token Refresh Token
   * @returns 用户ID
   */
  async validateRefreshToken(token: string): Promise<number> {
    try {
      // 验证JWT签名和过期时间
      const payload = this.jwtService.verify(token, {
        secret: jwtConstants.refreshTokenSecret,
      });

      // 检查数据库中的token是否存在且未被撤销
      const refreshTokenEntity = await this.refreshTokenRepository.findOne({
        where: {
          token,
          isRevoked: false,
        },
      });

      if (!refreshTokenEntity) {
        throw new BusinessException('Refresh Token无效');
      }

      // 检查是否过期
      if (refreshTokenEntity.expiresAt < new Date()) {
        await this.revokeRefreshToken(token);
        throw new BusinessException('Refresh Token已过期');
      }

      return payload.sub;
    } catch (error) {
      throw new BusinessException('Refresh Token无效');
    }
  }

  /**
   * 撤销Refresh Token
   * @param token Refresh Token
   */
  async revokeRefreshToken(token: string): Promise<void> {
    await this.refreshTokenRepository.update(
      { token },
      { isRevoked: true },
    );
  }

  /**
   * 撤销用户的所有Refresh Token
   * @param userId 用户ID
   */
  async revokeAllUserRefreshTokens(userId: number): Promise<void> {
    await this.refreshTokenRepository.update(
      { userId, isRevoked: false },
      { isRevoked: true },
    );
  }

  /**
   * 清理过期的Refresh Token
   */
  async cleanupExpiredTokens(): Promise<void> {
    await this.refreshTokenRepository.delete({
      expiresAt: LessThan(new Date()),
    });
  }

  /**
   * 获取用户的活跃设备列表
   * @param userId 用户ID
   * @returns 设备列表
   */
  async getUserActiveDevices(userId: number): Promise<RefreshToken[]> {
    return this.refreshTokenRepository.find({
      where: {
        userId,
        isRevoked: false,
        expiresAt: LessThan(new Date()),
      },
      order: {
        createdAt: 'DESC',
      },
    });
  }
}
