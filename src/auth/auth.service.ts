import { Injectable } from '@nestjs/common';
import { RegisterDto } from './dto/RegisterDto.dto';
import { LoginDto } from './dto/LoginDto.dto';
import { UserService } from 'src/user/user.service';
import { JwtService } from '@nestjs/jwt';
import * as bcrypt from 'bcrypt';
import {
  BusinessException,
  ResourceConflictException,
  AuthException
} from '../common/exceptions/business.exception';
import { RefreshTokenService } from './services/refresh-token.service';
import { jwtConstants } from './constants';

@Injectable()
export class AuthService {
  constructor(
    private readonly userService: UserService,
    private readonly jwtService: JwtService,
    private readonly refreshTokenService: RefreshTokenService,
  ) {}

  /**
   * 验证用户凭据（用于本地认证策略）
   * @param phoneNumber 手机号
   * @param password 密码
   * @returns 验证成功返回用户信息，失败返回null
   */
  async validateUser(phoneNumber: string, password: string): Promise<any> {
    const user = await this.userService.findOneByPhoneNumber(phoneNumber);
    if (user && await bcrypt.compare(password, user.password)) {
      // 移除密码字段，返回安全的用户信息
      const { password, ...result } = user;
      return result;
    }
    return null;
  }

  async register(registerDto: RegisterDto) {
    if(registerDto.password !== registerDto.confirmPassword){
      throw new BusinessException('两次输入的密码不一致');
    }
    if(await this.userService.findOneByPhoneNumber(registerDto.phoneNumber)){
      throw new ResourceConflictException('该手机号已被注册');
    }
    // 校验验证码
    if(registerDto.code !== '123456'){
      throw new BusinessException('验证码错误');
    }
    // hash加密
    const hashedPassword = await bcrypt.hash(registerDto.password, 10);
    registerDto.password = hashedPassword;
    await this.userService.create(registerDto);
    return {
      message: '注册成功',
    }
  }

  /**
   * 生成Access Token和Refresh Token（用于登录成功后）
   * @param user 用户信息
   * @param deviceInfo 设备信息
   * @param ipAddress IP地址
   * @returns 包含双token的响应
   */
  async login(user: any, deviceInfo?: string, ipAddress?: string) {
    const payload = {
      sub: user.id,
      username: user.username
    };

    // 生成Access Token（短期）
    const accessToken = this.jwtService.sign(payload, {
      secret: jwtConstants.secret,
      expiresIn: jwtConstants.accessTokenExpiresIn,
    });

    // 生成Refresh Token（长期）
    const refreshToken = await this.refreshTokenService.generateRefreshToken(
      user.id,
      deviceInfo,
      ipAddress,
    );

    return {
      message: '登录成功',
      access_token: accessToken,
      refresh_token: refreshToken,
      expires_in: jwtConstants.accessTokenExpiresIn,
    };
  }

  /**
   * 使用Refresh Token获取新的Access Token
   * @param refreshToken Refresh Token
   * @returns 新的Access Token
   */
  async refreshAccessToken(refreshToken: string) {
    // 验证Refresh Token
    const userId = await this.refreshTokenService.validateRefreshToken(refreshToken);

    // 获取用户信息
    const user = await this.userService.findOne(userId);
    if (!user) {
      throw new AuthException('用户不存在');
    }

    // 生成新的Access Token
    const payload = {
      sub: user.id,
      username: user.username
    };

    const newAccessToken = this.jwtService.sign(payload, {
      secret: jwtConstants.secret,
      expiresIn: jwtConstants.accessTokenExpiresIn,
    });

    return {
      message: 'Token刷新成功',
      access_token: newAccessToken,
      expires_in: jwtConstants.accessTokenExpiresIn,
    };
  }

  /**
   * 登出（撤销Refresh Token）
   * @param refreshToken Refresh Token
   */
  async logout(refreshToken: string) {
    await this.refreshTokenService.revokeRefreshToken(refreshToken);
    return {
      message: '登出成功',
    };
  }

  /**
   * 登出所有设备（撤销用户的所有Refresh Token）
   * @param userId 用户ID
   */
  async logoutAllDevices(userId: number) {
    await this.refreshTokenService.revokeAllUserRefreshTokens(userId);
    return {
      message: '已登出所有设备',
    };
  }

  /**
   * 获取当前用户信息
   * @param userId 用户ID
   * @returns 用户信息（不包含敏感数据）
   */
  async getCurrentUser(userId: number) {
    const user = await this.userService.findOne(userId);
    if (!user) {
      throw new AuthException('用户不存在');
    }

    // 移除敏感信息
    const { password, ...userInfo } = user;

    return {
      message: '获取用户信息成功',
      user: userInfo,
    };
  }
}
