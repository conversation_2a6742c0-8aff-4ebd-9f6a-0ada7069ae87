import { Injectable, UnauthorizedException } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { jwtConstants } from '../constants';
import { UserService } from '../../user/user.service';

export interface JwtPayload {
  sub: number;
  username: string;
  iat?: number;
  exp?: number;
}

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor(private readonly userService: UserService) {
    super({
      // 从Authorization header中提取Bearer token
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      // 是否忽略过期时间（false表示不忽略，会验证过期时间）
      ignoreExpiration: false,
      // JWT密钥
      secretOrKey: jwtConstants.secret,
    });
  }

  /**
   * 验证JWT payload
   * 这个方法会在JWT验证成功后自动调用
   * 返回的用户信息会被注入到request.user中
   */
  async validate(payload: JwtPayload) {
    // 可以在这里进行额外的用户验证
    // 比如检查用户是否仍然存在、是否被禁用等
    const user = await this.userService.findOne(payload.sub);
    if (!user) {
      throw new UnauthorizedException('用户不存在');
    }

    // 返回的对象会被设置到request.user上
    return {
      id: payload.sub,
      username: payload.username,
      // 可以添加更多用户信息
    };
  }
}
