import { Injectable } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { Strategy } from 'passport-local';
import { AuthService } from '../auth.service';
import { AuthException } from '../../common/exceptions/business.exception';

@Injectable()
export class LocalStrategy extends PassportStrategy(Strategy) {
  constructor(private authService: AuthService) {
    super({
      // 指定用户名字段（默认是username，我们改为phoneNumber）
      usernameField: 'phoneNumber',
      passwordField: 'password',
    });
  }

  /**
   * 验证用户凭据
   * 这个方法会在LocalAuthGuard触发时自动调用
   * @param phoneNumber 手机号
   * @param password 密码
   * @returns 验证成功返回用户信息，失败抛出异常
   */
  async validate(phoneNumber: string, password: string): Promise<any> {
    const user = await this.authService.validateUser(phoneNumber, password);
    if (!user) {
      throw new AuthException('用户名或密码错误');
    }
    return user;
  }
}
