import { SessionHistory } from "src/session_history/entities/session_history.entity";
import { RefreshToken } from "src/auth/entities/refresh-token.entity";
import { Entity, Column, PrimaryGeneratedColumn, OneToMany } from "typeorm";

@Entity()
export class User {
  @PrimaryGeneratedColumn()
  id: number
  @Column()
  username: string;

  @Column()
  password: string;

  @Column()
  phoneNumber: string;

  @OneToMany(() => SessionHistory, (sessionHistory) => sessionHistory.user)
  sessionHistory: SessionHistory[];

  @OneToMany(() => RefreshToken, (refreshToken) => refreshToken.user)
  refreshTokens: RefreshToken[];

}
