import {
  BusinessException,
  ValidationException,
  AuthException,
  AuthorizationException,
  ResourceNotFoundException,
  ResourceConflictException,
} from '../exceptions/business.exception';

/**
 * 异常工具类
 * 提供常用的异常抛出方法
 */
export class ExceptionUtil {
  /**
   * 抛出业务异常
   */
  static throwBusiness(message: string, code?: number): never {
    throw new BusinessException(message, code);
  }

  /**
   * 抛出验证异常
   */
  static throwValidation(message: string | string[]): never {
    throw new ValidationException(message);
  }

  /**
   * 抛出认证异常
   */
  static throwAuth(message?: string): never {
    throw new AuthException(message);
  }

  /**
   * 抛出授权异常
   */
  static throwAuthorization(message?: string): never {
    throw new AuthorizationException(message);
  }

  /**
   * 抛出资源不存在异常
   */
  static throwNotFound(resource?: string): never {
    throw new ResourceNotFoundException(resource);
  }

  /**
   * 抛出资源冲突异常
   */
  static throwConflict(message: string): never {
    throw new ResourceConflictException(message);
  }

  /**
   * 条件性抛出异常
   */
  static throwIf(condition: boolean, exception: () => never): void {
    if (condition) {
      exception();
    }
  }

  /**
   * 条件性抛出业务异常
   */
  static throwBusinessIf(condition: boolean, message: string, code?: number): void {
    if (condition) {
      throw new BusinessException(message, code);
    }
  }

  /**
   * 条件性抛出认证异常
   */
  static throwAuthIf(condition: boolean, message?: string): void {
    if (condition) {
      throw new AuthException(message);
    }
  }

  /**
   * 条件性抛出资源不存在异常
   */
  static throwNotFoundIf(condition: boolean, resource?: string): void {
    if (condition) {
      throw new ResourceNotFoundException(resource);
    }
  }
}
