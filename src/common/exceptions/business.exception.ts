import { HttpException, HttpStatus } from '@nestjs/common';

/**
 * 业务异常类
 * 用于处理业务逻辑相关的异常
 */
export class BusinessException extends HttpException {
  constructor(
    message: string,
    code: number = HttpStatus.BAD_REQUEST,
    error: string = 'Business Error'
  ) {
    super(
      {
        message,
        error,
        statusCode: code,
      },
      code,
    );
  }
}

/**
 * 验证异常类
 * 用于处理数据验证相关的异常
 */
export class ValidationException extends HttpException {
  constructor(message: string | string[]) {
    super(
      {
        message,
        error: 'Validation Error',
        statusCode: HttpStatus.BAD_REQUEST,
      },
      HttpStatus.BAD_REQUEST,
    );
  }
}

/**
 * 认证异常类
 * 用于处理认证相关的异常
 */
export class AuthException extends HttpException {
  constructor(message: string = '认证失败') {
    super(
      {
        message,
        error: 'Authentication Error',
        statusCode: HttpStatus.UNAUTHORIZED,
      },
      HttpStatus.UNAUTHORIZED,
    );
  }
}

/**
 * 授权异常类
 * 用于处理权限相关的异常
 */
export class AuthorizationException extends HttpException {
  constructor(message: string = '权限不足') {
    super(
      {
        message,
        error: 'Authorization Error',
        statusCode: HttpStatus.FORBIDDEN,
      },
      HttpStatus.FORBIDDEN,
    );
  }
}

/**
 * 资源不存在异常类
 */
export class ResourceNotFoundException extends HttpException {
  constructor(resource: string = '资源') {
    super(
      {
        message: `${resource}不存在`,
        error: 'Resource Not Found',
        statusCode: HttpStatus.NOT_FOUND,
      },
      HttpStatus.NOT_FOUND,
    );
  }
}

/**
 * 资源冲突异常类
 */
export class ResourceConflictException extends HttpException {
  constructor(message: string) {
    super(
      {
        message,
        error: 'Resource Conflict',
        statusCode: HttpStatus.CONFLICT,
      },
      HttpStatus.CONFLICT,
    );
  }
}
